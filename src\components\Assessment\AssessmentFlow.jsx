import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import AssessmentForm from './AssessmentForm';
import LoadingSpinner from '../UI/LoadingSpinner';
import { useAuth } from '../../context/AuthContext';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [assessmentResults, setAssessmentResults] = useState({
    via: null,
    riasec: null,
    bigFive: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tokenBalance, setTokenBalance] = useState(null);
  const [isCheckingToken, setIsCheckingToken] = useState(false);

  const assessments = [
    { key: 'via', data: viaQuestions, title: 'VIA Character Strengths' },
    { key: 'riasec', data: riasecQuestions, title: 'RIASEC Holland Codes' },
    { key: 'bigFive', data: bigFiveQuestions, title: 'Big Five Personality' }
  ];

  const currentAssessment = assessments[currentStep - 1];

  // Fetch token balance on component mount
  useEffect(() => {
    const fetchTokenBalance = async () => {
      try {
        const response = await apiService.getTokenBalance();
        if (response.success) {
          setTokenBalance(response.data.token_balance);
        }
      } catch (err) {
        console.error('Failed to fetch token balance:', err);
        setTokenBalance(0); // Set to 0 if failed to fetch
      }
    };

    fetchTokenBalance();
  }, []);

  // Function to check token balance before submission
  const checkTokenBalance = async () => {
    setIsCheckingToken(true);
    try {
      const response = await apiService.getTokenBalance();
      if (response.success) {
        const balance = response.data.token_balance;
        setTokenBalance(balance);
        return balance;
      }
      return 0;
    } catch (err) {
      console.error('Failed to check token balance:', err);
      return 0;
    } finally {
      setIsCheckingToken(false);
    }
  };

  const handleAssessmentComplete = (scores) => {
    setAssessmentResults(prev => ({
      ...prev,
      [currentAssessment.key]: scores
    }));

    // Automatically move to next assessment if not the last one
    if (currentStep < assessments.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleNext = () => {
    if (currentStep < assessments.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const transformScoresForAPI = () => {
    const { via, riasec, bigFive } = assessmentResults;

    // Transform VIA scores to match API format with all required fields
    const viaIs = {
      creativity: (via?.creativity || 0),
      curiosity: (via?.curiosity || 0),
      judgment: (via?.judgment || 0),
      loveOfLearning: (via?.loveOfLearning || 0),
      perspective: (via?.perspective || 0),
      bravery: (via?.bravery || 0),
      perseverance: (via?.perseverance || 0),
      honesty: (via?.honesty || 0),
      zest: (via?.zest || 0),
      love: (via?.love || 0),
      kindness: (via?.kindness || 0),
      socialIntelligence: (via?.socialIntelligence || 0),
      teamwork: (via?.teamwork || 0),
      fairness: (via?.fairness || 0),
      leadership: (via?.leadership || 0),
      forgiveness: (via?.forgiveness || 0),
      humility: (via?.humility || 0),
      prudence: (via?.prudence || 0),
      selfRegulation: (via?.selfRegulation || 0),
      appreciationOfBeauty: (via?.appreciationOfBeauty || 0),
      gratitude: (via?.gratitude || 0),
      hope: (via?.hope || 0),
      humor: (via?.humor || 0),
      spirituality: (via?.spirituality || 0)
    };

    // Transform RIASEC scores to match API format
    const riasecScores = {
      realistic: (riasec?.realistic || 0),
      investigative: (riasec?.investigative || 0),
      artistic: (riasec?.artistic || 0),
      social: (riasec?.social || 0),
      enterprising: (riasec?.enterprising || 0),
      conventional: (riasec?.conventional || 0)
    };

    // Transform Big Five scores to match API format (OCEAN)
    const ocean = {
      openness: (bigFive?.openness || 0),
      conscientiousness: (bigFive?.conscientiousness || 0),
      extraversion: (bigFive?.extraversion || 0),
      agreeableness: (bigFive?.agreeableness || 0),
      neuroticism: (bigFive?.neuroticism || 0)
    };

    const transformedData = {
      riasec: riasecScores,
      ocean: ocean,
      viaIs: viaIs
    };

    // Log the transformed data for debugging
    console.log('Assessment Results:', assessmentResults);
    console.log('Transformed Data for API:', transformedData);

    return transformedData;
  };

  const findIncompleteAssessment = () => {
    const { via, riasec, bigFive } = assessmentResults;
    if (!via) return { step: 1, name: 'VIA Character Strengths' };
    if (!riasec) return { step: 2, name: 'RIASEC Holland Codes' };
    if (!bigFive) return { step: 3, name: 'Big Five Personality' };
    return null;
  };

  const handleSubmitAll = async () => {
    // Validate that all assessments are completed
    const incompleteAssessment = findIncompleteAssessment();
    if (incompleteAssessment) {
      setError(`Please complete the ${incompleteAssessment.name} assessment before submitting. Redirecting you to that assessment...`);

      // Redirect to incomplete assessment after a short delay
      setTimeout(() => {
        setCurrentStep(incompleteAssessment.step);
        setError('');
      }, 2000);
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Check token balance before submitting
      const currentBalance = await checkTokenBalance();

      if (currentBalance <= 0) {
        setError('Insufficient token balance. You need at least 1 token to submit an assessment. Please contact support to add more tokens to your account.');
        setIsSubmitting(false);
        return;
      }

      const transformedData = transformScoresForAPI();

      const response = await apiService.submitAssessment(transformedData);

      if (response.success) {
        const { jobId } = response.data;
        // Navigate to status page with jobId
        navigate(`/assessment/status/${jobId}`);
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to submit assessment');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner
          size="xl"
          text="Submitting Assessment... Please wait while we process your responses."
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Token Balance Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Assessment</h1>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Token Balance:
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                tokenBalance !== null && tokenBalance > 0
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {tokenBalance !== null ? tokenBalance : '...'} tokens
              </div>
              {isCheckingToken && (
                <div className="text-sm text-gray-500">Checking...</div>
              )}
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 m-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      <AssessmentForm
        key={currentStep} // Force re-render when step changes to reset state
        assessmentData={currentAssessment.data}
        onSubmit={currentStep === assessments.length ? handleSubmitAll : handleAssessmentComplete}
        onNext={handleNext}
        onPrevious={handlePrevious}
        isLastAssessment={currentStep === assessments.length}
        currentStep={currentStep}
        totalSteps={assessments.length}
      />
    </div>
  );
};

export default AssessmentFlow;
