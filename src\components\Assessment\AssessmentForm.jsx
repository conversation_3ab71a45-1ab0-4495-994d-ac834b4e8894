import { useState, useEffect } from 'react';
import AssessmentQuestion from './AssessmentQuestion';
import ProgressBar from '../UI/ProgressBar';

const AssessmentForm = ({ 
  assessmentData, 
  onSubmit, 
  onNext, 
  onPrevious, 
  isLastAssessment = false,
  currentStep = 1,
  totalSteps = 3
}) => {
  const [answers, setAnswers] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const questionsPerPage = 5;

  // Scroll to top when page changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [currentPage]);
  
  // Flatten all questions from all categories
  const allQuestions = [];
  Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
    // Regular questions
    category.questions.forEach((question, index) => {
      allQuestions.push({
        question,
        categoryKey,
        questionKey: `${categoryKey}_${index}`,
        isReverse: false
      });
    });
    
    // Reverse questions (for Big Five)
    if (category.reverseQuestions) {
      category.reverseQuestions.forEach((question, index) => {
        allQuestions.push({
          question,
          categoryKey,
          questionKey: `${categoryKey}_reverse_${index}`,
          isReverse: true
        });
      });
    }
  });

  const totalPages = Math.ceil(allQuestions.length / questionsPerPage);
  const currentQuestions = allQuestions.slice(
    currentPage * questionsPerPage,
    (currentPage + 1) * questionsPerPage
  );

  const handleAnswerChange = (questionKey, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionKey]: value
    }));
  };

  const handleNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 0) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const calculateScores = () => {
    const scores = {};
    
    Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
      let totalScore = 0;
      let questionCount = 0;
      
      // Regular questions
      category.questions.forEach((_, index) => {
        const questionKey = `${categoryKey}_${index}`;
        if (answers[questionKey]) {
          totalScore += answers[questionKey];
          questionCount++;
        }
      });
      
      // Reverse questions (for Big Five)
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((_, index) => {
          const questionKey = `${categoryKey}_reverse_${index}`;
          if (answers[questionKey]) {
            // Reverse the score (6 - original score for 1-5 scale)
            totalScore += (6 - answers[questionKey]);
            questionCount++;
          }
        });
      }
      
      // Calculate average score (0-100 scale)
      if (questionCount > 0) {
        scores[categoryKey] = Math.round((totalScore / questionCount) * 20); // Convert 1-5 to 0-100
      }
    });
    
    return scores;
  };

  const handleSubmit = () => {
    const scores = calculateScores();
    onSubmit(scores);
  };

  const isAssessmentComplete = () => {
    return allQuestions.every(q => answers[q.questionKey] !== undefined);
  };

  const progress = (Object.keys(answers).length / allQuestions.length) * 100;

  // Function to find first unanswered question
  const findFirstUnansweredQuestion = () => {
    for (let i = 0; i < allQuestions.length; i++) {
      if (answers[allQuestions[i].questionKey] === undefined) {
        return Math.floor(i / questionsPerPage);
      }
    }
    return currentPage;
  };

  // Enhanced submit handler with validation
  const handleSubmitWithValidation = () => {
    if (!isAssessmentComplete()) {
      const firstUnansweredPage = findFirstUnansweredQuestion();
      setCurrentPage(firstUnansweredPage);
      // You could also show a toast or alert here
      return;
    }
    handleSubmit();
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Main Content */}
      <div className="flex-1 lg:mr-80 p-6 pt-20 lg:pt-6">
        {/* Assessment Header - Only show on desktop */}
        <div className="hidden lg:block mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {assessmentData.title}
          </h1>
          <div className="text-sm text-gray-500 mb-4">
            Assessment {currentStep} of {totalSteps}
          </div>
          <p className="text-sm text-gray-600 mb-4">
            {assessmentData.description}
          </p>

          {/* Progress Bar */}
          <div className="mb-6">
            <ProgressBar
              progress={progress}
              label={`Progress: ${Object.keys(answers).length}/${allQuestions.length} questions`}
              showPercentage={true}
            />
          </div>
        </div>

        {/* Questions */}
        <div className="max-w-4xl">
          {currentQuestions.map((q, index) => (
            <AssessmentQuestion
              key={q.questionKey}
              question={q.question}
              questionIndex={currentPage * questionsPerPage + index}
              totalQuestions={allQuestions.length}
              scale={assessmentData.scale}
              value={answers[q.questionKey]}
              onChange={(value) => handleAnswerChange(q.questionKey, value)}
              isReverse={q.isReverse}
            />
          ))}
        </div>

        {/* Bottom Navigation - Always visible */}
        <div className="mt-8 flex justify-between items-center">
          <div className="flex space-x-4">
            {/* Previous Page Button */}
            {currentPage > 0 && (
              <button
                onClick={handlePreviousPage}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                ← Previous Page
              </button>
            )}

            {/* Previous Assessment Button */}
            {currentStep > 1 && currentPage === 0 && (
              <button
                onClick={onPrevious}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                ← Previous Assessment
              </button>
            )}
          </div>

          <div className="flex space-x-4">
            {/* Next Page Button */}
            {currentPage < totalPages - 1 && (
              <button
                onClick={handleNextPage}
                className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
              >
                Next Page →
              </button>
            )}

            {/* Next Assessment Button */}
            {currentPage === totalPages - 1 && !isLastAssessment && (
              <button
                onClick={onNext}
                disabled={!isAssessmentComplete()}
                className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title={!isAssessmentComplete() ? "Please complete all questions before proceeding" : ""}
              >
                Next Assessment →
              </button>
            )}

            {/* Submit Assessment Button */}
            {currentPage === totalPages - 1 && isLastAssessment && (
              <button
                onClick={handleSubmitWithValidation}
                disabled={!isAssessmentComplete()}
                className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title={!isAssessmentComplete() ? "Please complete all questions before submitting" : ""}
              >
                Submit All Assessments
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Right Side Navigation */}
      <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-lg border-l border-gray-200 p-6 overflow-y-auto z-10 hidden lg:block">
        {/* Page Navigation */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Page Navigation
          </h3>

          {/* Page Indicator */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-center text-sm font-medium text-gray-700">
              Page {currentPage + 1} of {totalPages}
            </div>
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentPage + 1) / totalPages) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Quick Jump - Dynamic based on total pages */}
          {totalPages > 1 && (
            <div className="pt-2">
              <div className="text-xs text-gray-500 mb-2">Quick Jump to Page:</div>
              <div className={`grid gap-1 ${totalPages <= 10 ? 'grid-cols-5' : totalPages <= 20 ? 'grid-cols-4' : 'grid-cols-3'}`}>
                {Array.from({ length: totalPages }, (_, i) => (
                  <button
                    key={i}
                    onClick={() => setCurrentPage(i)}
                    className={`px-2 py-1 text-xs rounded ${
                      currentPage === i
                        ? 'bg-indigo-600 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    } transition-colors`}
                  >
                    {i + 1}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden fixed top-0 left-0 right-0 bg-white shadow-md border-b border-gray-200 p-4 z-20">
        <div className="flex justify-between items-center">
          <div className="text-sm font-medium text-gray-700">
            Page {currentPage + 1} of {totalPages}
          </div>
          <div className="flex space-x-2">
            {currentPage > 0 && (
              <button
                onClick={handlePreviousPage}
                className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50"
              >
                ← Prev
              </button>
            )}
            {currentPage < totalPages - 1 && (
              <button
                onClick={handleNextPage}
                className="px-3 py-1 bg-indigo-600 text-white rounded text-sm hover:bg-indigo-700"
              >
                Next →
              </button>
            )}
          </div>
        </div>
        <div className="mt-2 w-full bg-gray-200 rounded-full h-1">
          <div
            className="bg-indigo-600 h-1 rounded-full transition-all duration-300"
            style={{ width: `${((currentPage + 1) / totalPages) * 100}%` }}
          ></div>
        </div>

        {/* Mobile Progress Info */}
        <div className="mt-2 text-xs text-gray-500 text-center">
          {Object.keys(answers).length}/{allQuestions.length} questions completed
        </div>
      </div>
    </div>
  );
};

export default AssessmentForm;
